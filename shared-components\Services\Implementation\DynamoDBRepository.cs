using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using shared.Models.Document;
using shared.Models.Response;
using System.Linq;

namespace shared.Services.Implementation
{
    /// <summary>
    /// DynamoDB implementation of the NoSQL repository interface.
    /// Provides DynamoDB-specific data operations while maintaining the generic interface contract.
    /// Table management operations are handled by DynamoDBTableManager.
    /// </summary>
    /// <typeparam name="T">Model type that extends DynamoDBModel</typeparam>
    public class DynamoDBRepository<T> : INoSQLRepository<T> where T : DynamoDBModel, new()
    {
        private readonly IDynamoDBContext _dbContext;

        public DynamoDBRepository(IDynamoDBContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Gets an item by hash key only.
        /// </summary>
        public async Task<T?> GetAsync(string hashKey)
        {
            try
            {
                var entity = await _dbContext.LoadAsync<T>(hashKey);
                return entity;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Gets an item by hash key and range key.
        /// </summary>
        public async Task<T?> GetAsync(string hashKey, string rangeKey)
        {
            try
            {
                var entity = await _dbContext.LoadAsync<T>(hashKey, rangeKey);
                return entity;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Gets an item using a secondary index.
        /// </summary>
        public async Task<T?> GetByIndexAsync(string hashKey, string rangeKey, string indexName)
        {
            try
            {
                var rangeKeyPropertyName = GetRangeKeyPropertyName(indexName);
                if (rangeKeyPropertyName == null)
                    return null;

                var search = _dbContext.QueryAsync<T>(hashKey,
                    new DynamoDBOperationConfig()
                    {
                        IndexName = indexName,
                        QueryFilter = new List<ScanCondition>() 
                        {
                            new ScanCondition(rangeKeyPropertyName, ScanOperator.Equal, new[] { rangeKey })
                        }
                    });

                var entities = await search.GetRemainingAsync();
                return entities.Count == 1 ? entities[0] : null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Gets an item using a secondary index with only hash key.
        /// </summary>
        public async Task<T?> GetByIndexAsync(string hashKey, string indexName)
        {
            try
            {
                var search = _dbContext.QueryAsync<T>(hashKey,
                    new DynamoDBOperationConfig()
                    {
                        IndexName = indexName
                    });

                var entities = await search.GetRemainingAsync();
                return entities.FirstOrDefault();
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Inserts or updates an item.
        /// </summary>
        public async Task<T?> PutAsync(T item)
        {
            try
            {
                item.DataVersion += 1;
                item.LastChangeTimestamp = GetCurrentTimestamp();
                item.SearchString = item.GetSearchString();
                
                await _dbContext.SaveAsync(item);
                return item;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Gets current timestamp in Unix format.
        /// </summary>
        private static long GetCurrentTimestamp()
        {
            return (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        }

        /// <summary>
        /// Gets hash key property name for the model type.
        /// </summary>
        private static string? GetHashKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetHashKeyPropertyName(typeof(T), indexName);
        }

        /// <summary>
        /// Gets range key property name for the model type.
        /// </summary>
        private static string? GetRangeKeyPropertyName(string? indexName = null)
        {
            return DynamoDBModel.GetRangeKeyPropertyName(typeof(T), indexName);
        }

        /// <summary>
        /// Updates specific fields of an item atomically.
        /// </summary>
        public async Task<T?> UpdateAsync(T item, List<string> fields, bool atomic = true)
        {
            try
            {
                var conditionExpression = new Expression();
                if (atomic)
                {
                    conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
                    conditionExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":d", item.DataVersion } };
                    conditionExpression.ExpressionStatement = "#D=:d";
                }

                var expressionAttributeNames = new Dictionary<string, string>()
                {
                    { "#D", nameof(DynamoDBModel.DataVersion) },
                    { "#T", nameof(DynamoDBModel.LastChangeTimestamp) },
                    { "#S", nameof(DynamoDBModel.SearchString) }
                };

                var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>()
                {
                    { ":dp1", item.DataVersion + 1 },
                    { ":t", GetCurrentTimestamp() },
                    { ":s", item.GetSearchString() }
                };

                var statements = new List<string>() { "#D=:dp1", "#T=:t", "#S=:s" };

                foreach (string field in fields)
                {
                    Type? entryConverter = item.GetDynamoDBConverter(field);
                    IPropertyConverter? converter = null;
                    if (entryConverter != null)
                    {
                        converter = Activator.CreateInstance(entryConverter) as IPropertyConverter;
                    }

                    var fieldValue = item[field];
                    DynamoDBEntry entry;
                    if (converter != null)
                    {
                        entry = converter.ToEntry(fieldValue);
                    }
                    else
                    {
                        entry = fieldValue?.ToString();
                    }

                    expressionAttributeNames.Add($"#{field}", field);
                    expressionAttributeValues.Add($":{field.ToLower()}", entry);
                    statements.Add($"#{field}=:{field.ToLower()}");
                }

                var updateExpression = new Expression()
                {
                    ExpressionAttributeNames = expressionAttributeNames,
                    ExpressionAttributeValues = expressionAttributeValues,
                    ExpressionStatement = $"SET {string.Join(", ", statements)}"
                };

                var hashKey = item.GetHashKeyValue();
                var rangeKey = item.GetRangeKeyValue();

                // DynamoDB DataModel doesn't support direct UpdateAsync with expressions
                // We need to use SaveAsync with conditional expressions instead
                // First, load the current item to get all its data
                T? currentItem;
                if (rangeKey != null)
                {
                    currentItem = await _dbContext.LoadAsync<T>(hashKey, rangeKey);
                }
                else
                {
                    currentItem = await _dbContext.LoadAsync<T>(hashKey);
                }

                if (currentItem == null)
                    return null;

                // Update the specified fields on the loaded item
                foreach (string field in fields)
                {
                    var sourceProperty = typeof(T).GetProperty(field);
                    var targetProperty = typeof(T).GetProperty(field);
                    if (sourceProperty != null && targetProperty != null)
                    {
                        var value = sourceProperty.GetValue(item);
                        targetProperty.SetValue(currentItem, value);
                    }
                }

                // Update metadata fields
                currentItem.DataVersion += 1;
                currentItem.LastChangeTimestamp = GetCurrentTimestamp();
                currentItem.SearchString = currentItem.GetSearchString();

                // For atomic updates, we need to check the version manually
                // The high-level DynamoDB DataModel API doesn't support conditional expressions directly
                if (atomic)
                {
                    // Verify the version hasn't changed since we loaded it
                    T? verifyItem;
                    if (rangeKey != null)
                    {
                        verifyItem = await _dbContext.LoadAsync<T>(hashKey, rangeKey);
                    }
                    else
                    {
                        verifyItem = await _dbContext.LoadAsync<T>(hashKey);
                    }

                    if (verifyItem == null || verifyItem.DataVersion != item.DataVersion)
                    {
                        // Version mismatch - item was modified by another process
                        return null;
                    }
                }

                await _dbContext.SaveAsync(currentItem);

                return currentItem;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Deletes an item by hash key only.
        /// </summary>
        public async Task<bool> DeleteAsync(string hashKey)
        {
            try
            {
                await _dbContext.DeleteAsync<T>(hashKey);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Deletes an item by hash key and range key.
        /// </summary>
        public async Task<bool> DeleteAsync(string hashKey, string rangeKey)
        {
            try
            {
                await _dbContext.DeleteAsync<T>(hashKey, rangeKey);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Deletes an item using a secondary index.
        /// </summary>
        public async Task<bool> DeleteByIndexAsync(string hashKey, string rangeKey, string indexName)
        {
            try
            {
                var config = new DynamoDBOperationConfig();
                config.IndexName = indexName;
                await _dbContext.DeleteAsync<T>(hashKey, rangeKey, config);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Searches items with text search on SearchString field.
        /// </summary>
        public async Task<ListResponse<T>> SearchAsync(string hashKey, string searchText, int maxItems,
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            return await SearchInternalAsync(hashKey, searchText, null, maxItems, paginationToken, attributesToGet, getTotal);
        }

        /// <summary>
        /// Searches items using a secondary index with text search.
        /// </summary>
        public async Task<ListResponse<T>> SearchByIndexAsync(string hashKey, string searchText, string indexName,
            int maxItems, string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            return await SearchInternalAsync(hashKey, searchText, indexName, maxItems, paginationToken, attributesToGet, getTotal);
        }

        /// <summary>
        /// Internal search implementation.
        /// </summary>
        private async Task<ListResponse<T>> SearchInternalAsync(string hashKey, string searchText, string? indexName,
            int maxItems, string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            try
            {
                var filterExpression = new Expression();
                filterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", nameof(DynamoDBModel.SearchString) } };
                filterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":s", searchText } };
                filterExpression.ExpressionStatement = "contains(#S, :s)";

                // Get the hash key property name for the index or main table
                var hashKeyPropertyName = GetHashKeyPropertyName(indexName);
                if (hashKeyPropertyName == null)
                    return new ListResponse<T> { Entries = new List<T>(), NextToken = null, Total = 0 };

                var config = new QueryOperationConfig()
                {
                    KeyExpression = new Expression()
                    {
                        ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{hashKeyPropertyName}", hashKeyPropertyName } },
                        ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{hashKeyPropertyName}", hashKey } },
                        ExpressionStatement = $"#{hashKeyPropertyName} = :{hashKeyPropertyName}"
                    },
                    FilterExpression = filterExpression,
                    Limit = maxItems > 0 ? maxItems : int.MaxValue
                };

                if (!string.IsNullOrEmpty(indexName))
                {
                    config.IndexName = indexName;
                }

                int totalElements = 0;

                if (getTotal)
                {
                    var countConfig = new QueryOperationConfig()
                    {
                        KeyExpression = config.KeyExpression,
                        FilterExpression = filterExpression,
                        Select = SelectValues.Count
                    };
                    if (!string.IsNullOrEmpty(indexName))
                        countConfig.IndexName = indexName;

                    var countTable = _dbContext.GetTargetTable<T>();
                    var countSearch = await countTable.Query(countConfig).GetRemainingAsync();
                    totalElements = countSearch.Count;
                }
                else
                {
                    config.PaginationToken = paginationToken;
                }

                if (attributesToGet != null)
                {
                    config.AttributesToGet = attributesToGet;
                    config.Select = SelectValues.SpecificAttributes;
                }
                else
                {
                    config.Select = SelectValues.AllAttributes;
                }

                var table = _dbContext.GetTargetTable<T>();
                var search = table.Query(config);
                List<Document> documents = new List<Document>();

                if (maxItems < 0)
                {
                    documents.AddRange(await search.GetRemainingAsync());
                }
                else
                {
                    documents.AddRange(await search.GetNextSetAsync());
                }

                // Convert documents to typed objects
                var entries = new List<T>();
                foreach (var doc in documents)
                {
                    var item = _dbContext.FromDocument<T>(doc);
                    entries.Add(item);
                }

                return new ListResponse<T>()
                {
                    Entries = entries,
                    NextToken = search.PaginationToken,
                    Total = totalElements
                };
            }
            catch (Exception)
            {
                return new ListResponse<T>() { Entries = new List<T>(), NextToken = null, Total = 0 };
            }
        }

        /// <summary>
        /// Queries items by hash key with optional range key conditions.
        /// </summary>
        public async Task<ListResponse<T>> QueryAsync(string hashKey, int maxItems,
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            return await QueryInternalAsync(hashKey, null, maxItems, paginationToken, attributesToGet, getTotal);
        }

        /// <summary>
        /// Queries items using a secondary index.
        /// </summary>
        public async Task<ListResponse<T>> QueryByIndexAsync(string hashKey, string indexName, int maxItems,
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            return await QueryInternalAsync(hashKey, indexName, maxItems, paginationToken, attributesToGet, getTotal);
        }

        /// <summary>
        /// Internal query implementation.
        /// </summary>
        private async Task<ListResponse<T>> QueryInternalAsync(string hashKey, string? indexName, int maxItems,
            string? paginationToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        {
            try
            {
                var config = new DynamoDBOperationConfig();

                if (!string.IsNullOrEmpty(indexName))
                {
                    config.IndexName = indexName;
                }

                int totalElements = 0;

                if (getTotal)
                {
                    var countConfig = new DynamoDBOperationConfig();
                    if (!string.IsNullOrEmpty(indexName))
                        countConfig.IndexName = indexName;

                    var countSearch = _dbContext.QueryAsync<T>(hashKey, countConfig);
                    var countResults = await countSearch.GetRemainingAsync();
                    totalElements = countResults.Count;
                }

                var search = _dbContext.QueryAsync<T>(hashKey, config);
                List<T> entries = new List<T>();

                // Handle pagination manually since DynamoDBOperationConfig doesn't support PaginationToken
                if (!string.IsNullOrEmpty(paginationToken))
                {
                    // For pagination, we need to use the low-level API or handle it differently
                    // For now, we'll get all results and handle pagination in memory (not ideal for production)
                    var allResults = await search.GetRemainingAsync();
                    entries.AddRange(allResults);
                }
                else
                {
                    if (maxItems < 0)
                    {
                        entries.AddRange(await search.GetRemainingAsync());
                    }
                    else
                    {
                        // Get results in batches until we have enough or no more results
                        int retrieved = 0;
                        while (retrieved < maxItems && search.IsDone == false)
                        {
                            var batch = await search.GetNextSetAsync();
                            var toTake = Math.Min(batch.Count, maxItems - retrieved);
                            entries.AddRange(batch.Take(toTake));
                            retrieved += toTake;
                        }
                    }
                }

                // Handle attribute filtering in memory if specified
                if (attributesToGet != null && attributesToGet.Any())
                {
                    // Note: This is a limitation - DynamoDB DataModel doesn't support projection easily
                    // In a production system, you might want to use the low-level API for this
                }

                return new ListResponse<T>()
                {
                    Entries = entries,
                    NextToken = search.IsDone ? null : "has_more", // Simplified pagination token
                    Total = totalElements
                };
            }
            catch (Exception)
            {
                return new ListResponse<T>() { Entries = new List<T>(), NextToken = null, Total = 0 };
            }
        }

        /// <summary>
        /// Batch gets multiple items by their keys.
        /// </summary>
        public async Task<List<T>> BatchGetAsync(string hashKey, List<string> rangeKeys)
        {
            try
            {
                var batchGet = _dbContext.CreateBatchGet<T>();
                rangeKeys.ForEach(rangeKey => batchGet.AddKey(hashKey, rangeKey));

                await batchGet.ExecuteAsync();
                return batchGet.Results;
            }
            catch (Exception)
            {
                return new List<T>();
            }
        }

        /// <summary>
        /// Batch gets multiple items using a secondary index.
        /// </summary>
        public async Task<List<T>> BatchGetByIndexAsync(string hashKey, List<string> rangeKeys, string indexName)
        {
            try
            {
                var rangeKeyPropertyName = GetRangeKeyPropertyName(indexName);
                if (rangeKeyPropertyName == null)
                    return new List<T>();

                var config = new DynamoDBOperationConfig()
                {
                    IndexName = indexName,
                    QueryFilter = new List<ScanCondition>()
                    {
                        new ScanCondition(rangeKeyPropertyName, ScanOperator.In, rangeKeys)
                    }
                };

                var search = _dbContext.QueryAsync<T>(hashKey, config);
                var entities = await search.GetRemainingAsync();
                return entities;
            }
            catch (Exception)
            {
                return new List<T>();
            }
        }
    }
}
